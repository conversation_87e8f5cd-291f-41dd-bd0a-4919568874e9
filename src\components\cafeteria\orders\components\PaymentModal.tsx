'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { currencyFormat } from '@/lib/utils';
import { toast } from 'sonner';
import { myApi } from '@/api/fetcher';

interface PaymentModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  unpaidAmount: number;
  onPaymentSuccess?: () => void;
}

export default function PaymentModal({
  open,
  setOpen,
  unpaidAmount,
  onPaymentSuccess,
}: PaymentModalProps) {
  const [paymentMode, setPaymentMode] = useState<string>('');
  const [amount, setAmount] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleClose = () => {
    setOpen(false);
    setPaymentMode('');
    setAmount('');
  };

  const handlePayment = async () => {
    if (!paymentMode) {
      toast.error('Please select a payment mode');
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (parseFloat(amount) > unpaidAmount) {
      toast.error('Amount cannot exceed unpaid credit');
      return;
    }

    try {
      setIsProcessing(true);

      const payload = {
        paymentMode,
        amount: parseFloat(amount),
      };

      const response = await myApi.post('/cafeteria/orders/pay-credit', payload);

      if (response.status === 200) {
        toast.success('Payment processed successfully');
        handleClose();
        if (onPaymentSuccess) {
          onPaymentSuccess();
        }
      }
    } catch (error) {
      console.error('Payment failed:', error);
      toast.error('Payment failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const paymentModes = [
    { value: 'deduction', label: 'Deduction' },
    { value: 'transfer', label: 'Transfer' },
    { value: 'card', label: 'Card' },
  ];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Pay Unpaid Credit</DialogTitle>
          <DialogDescription>
            Outstanding amount: {currencyFormat(unpaidAmount)}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="paymentMode">Payment Mode</Label>
            <Select value={paymentMode} onValueChange={setPaymentMode}>
              <SelectTrigger>
                <SelectValue placeholder="Select payment mode" />
              </SelectTrigger>
              <SelectContent>
                {paymentModes.map((mode) => (
                  <SelectItem key={mode.value} value={mode.value}>
                    {mode.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="amount">Amount</Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              min="0"
              max={unpaidAmount}
              step="0.01"
            />
            <p className="text-xs text-muted-foreground">
              Maximum: {currencyFormat(unpaidAmount)}
            </p>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isProcessing}>
            Cancel
          </Button>
          <Button onClick={handlePayment} disabled={isProcessing}>
            {isProcessing ? 'Processing...' : 'Pay'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
