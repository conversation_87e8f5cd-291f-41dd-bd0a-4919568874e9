'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Modal } from '@/components/common/modal';
import { Form } from '@/components/ui/form';
import { InputTextArea } from '@/components/common/form';
import {
  CommentFormValues,
  commentSchema,
} from '@/components/validations/idea';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

export const CommentModal = ({
  idea,
  open,
  onOpenChange,
  onCommentSubmit,
}: {
  idea: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCommentSubmit: (commentText: string) => void;
}) => {
  const form = useForm<CommentFormValues>({
    resolver: zodResolver(commentSchema),
    defaultValues: { comment: '' },
  });

  if (!idea) return null;

  const onSubmit = (values: CommentFormValues) => {
    onCommentSubmit(values.comment);
    form.reset();
  };

  return (
    <Modal
      open={open}
      setOpen={onOpenChange}
      title="Comments "
      description={`Read and contribute to the discussion about ${idea.title}`}
      size="lg"
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <div className="space-y-4 max-h-[60vh] overflow-y-auto">
        {idea.comments.length > 0 ? (
          idea.comments.map((comment: any) => (
            <div key={comment.id} className="flex items-start space-x-3">
              <div className="flex-1">
                <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3">
                  <p className="font-semibold text-xs">
                    {comment.staff.fullName}
                  </p>
                  <p className="text-sm">{comment.content}</p>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {dayjs(comment.createdAt).fromNow()}
                </p>
              </div>
            </div>
          ))
        ) : (
          <p className="text-center text-gray-500 dark:text-gray-400 py-8">
            No comments yet. Be the first to share your thoughts!
          </p>
        )}
      </div>
      <div className="mt-6 border-t pt-4">
        <Form {...form}>
          <form>
            <InputTextArea
              control={form.control}
              name="comment"
              label="Add a comment"
              placeholder="Write your comment here..."
            />
            {/* <Button type="submit">Comment</Button> */}
          </form>
        </Form>
      </div>
    </Modal>
  );
};
