'use client';

import React from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown } from 'lucide-react';
import Profile from '../profile';
import ProfileDetails from '../profile/profile-details';
import ChangePassword from '../profile/change-password';
import { ThemeToggle } from '../theme-toggle';
import { GetProfile } from '@/api/staff';
import { NotificationBell } from '../notification-bell';
import Breadcrumb from './breadcrumb';

export default function TopNav() {
  const { profile, isLoading } = GetProfile();
  const [dropdownOpen, setDropdownOpen] = React.useState(false);
  const [showProfileModal, setShowProfileModal] = React.useState(false);
  const [showPasswordModal, setShowPasswordModal] = React.useState(false);

  const handleViewProfile = () => {
    setDropdownOpen(false);
    setTimeout(() => {
      setShowProfileModal(true);
    }, 100);
  };

  const handleChangePassword = () => {
    setDropdownOpen(false);
    setTimeout(() => {
      setShowPasswordModal(true);
    }, 100);
  };

  const handleManageStaffs = () => {
    setDropdownOpen(false);
    setTimeout(() => {
      window.location.href = '/manage-staffs';
    }, 100);
  };

  const handleSystemSettings = () => {
    setDropdownOpen(false);
    setTimeout(() => {
      window.location.href = '/system-settings';
    }, 100);
  };

  return (
    <>
      <nav className="px-3 sm:px-6 flex items-center justify-between bg-white dark:bg-[#0F0F12] border-b border-gray-200 dark:border-[#1F1F23] h-full">
        <div className="flex-1 min-w-0">
          <Breadcrumb />
        </div>
        <div className="flex items-center gap-2 sm:gap-4 flex-shrink-0">
          <NotificationBell />
          <ThemeToggle />
          <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
            <DropdownMenuTrigger className="focus:outline-none text-sm flex items-center gap-1 border py-2 px-3 cursor-pointer rounded-full">
              {profile?.data.staffCode}
              <ChevronDown className="h-4 w-4" />
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              sideOffset={8}
              className="w-56 bg-background border-border rounded-lg shadow-lg"
            >
              <Profile
                user={profile?.data}
                closeDropdown={() => setDropdownOpen(false)}
                onViewProfile={handleViewProfile}
                onChangePassword={handleChangePassword}
                onManageStaffs={handleManageStaffs}
                onSystemSettings={handleSystemSettings}
              />
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </nav>

      {/* Profile Details Modal - Rendered outside the dropdown */}
      {profile?.data && (
        <>
          <div id="profile-modal-container">
            <ProfileDetails
              open={showProfileModal}
              setOpen={setShowProfileModal}
              user={profile.data}
            />
          </div>

          <div id="password-modal-container">
            <ChangePassword
              profile={profile.data}
              open={showPasswordModal}
              setOpen={setShowPasswordModal}
            />
          </div>
        </>
      )}
    </>
  );
}
