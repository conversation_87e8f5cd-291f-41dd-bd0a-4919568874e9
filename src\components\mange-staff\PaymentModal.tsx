'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { numberFormat } from '@/lib/utils';
import { toast } from 'sonner';
import { myApi } from '@/api/fetcher';

interface PaymentModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  unpaidAmount: number;
  staffId: string;
  staffName: string;
  walletBalance: number;
  mealVoucherBalance: number;
  onPaymentSuccess?: () => void;
}

export default function PaymentModal({
  open,
  setOpen,
  unpaidAmount,
  staffId,
  staffName,
  walletBalance,
  mealVoucherBalance,
  onPaymentSuccess,
}: PaymentModalProps) {
  const [paymentMode, setPaymentMode] = useState<string>('');
  const [amount, setAmount] = useState<string>('');
  const [displayAmount, setDisplayAmount] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Calculate payment breakdown
  const totalAvailableFunds = walletBalance + mealVoucherBalance;
  const walletCanCover = walletBalance >= unpaidAmount;
  const mealVoucherCanCover = mealVoucherBalance >= unpaidAmount;
  const bothCanCover = totalAvailableFunds >= unpaidAmount;
  const remainingBalance = bothCanCover ? 0 : unpaidAmount - totalAvailableFunds;
  const needsAdditionalPayment = remainingBalance > 0;

  const handleClose = () => {
    setOpen(false);
    setPaymentMode('');
    setAmount('');
    setDisplayAmount('');
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Remove all non-numeric characters except decimal point
    const value = e.target.value.replace(/[^\d.]/g, '');

    // Ensure only one decimal point
    const parts = value.split('.');
    if (parts.length > 2) {
      return;
    }

    // Limit decimal places to 2
    if (parts[1] && parts[1].length > 2) {
      return;
    }

    setAmount(value);

    // Format display value with commas
    if (value) {
      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        setDisplayAmount(new Intl.NumberFormat('en-US', {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        }).format(numValue));
      } else {
        setDisplayAmount(value);
      }
    } else {
      setDisplayAmount('');
    }
  };

  const handlePayment = async () => {
    // If additional payment is needed, validate payment mode and amount
    if (needsAdditionalPayment) {
      if (!paymentMode) {
        toast.error('Please select a payment mode');
        return;
      }

      if (!amount || parseFloat(amount) <= 0) {
        toast.error('Please enter a valid amount');
        return;
      }

      if (parseFloat(amount) !== remainingBalance) {
        toast.error(`Amount must be exactly ${numberFormat(remainingBalance)}`);
        return;
      }
    }

    try {
      setIsProcessing(true);

      const payload: any = {
        staffCode: staffId,
        amount: unpaidAmount, // Always send the full unpaid amount
      };

      // Only include payment mode if additional payment is needed
      if (needsAdditionalPayment) {
        payload.mode = paymentMode;
      }

      const response = await myApi.post('/staff/pay-credit', payload);

      if (response.status === 200) {
        toast.success('Payment processed successfully');
        handleClose();
        if (onPaymentSuccess) {
          onPaymentSuccess();
        }
      }
    } catch (error) {
      console.error('Payment failed:', error);
      toast.error('Payment failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const paymentModes = [
    { value: 'deduction', label: 'Deduction' },
    { value: 'transfer', label: 'Transfer' },
    { value: 'card', label: 'Card' },
  ];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Pay Unpaid Credit</DialogTitle>
          <DialogDescription>
            Processing payment for {staffName}
            <br />
            Outstanding amount: {numberFormat(unpaidAmount)}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {/* Payment Breakdown */}
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg space-y-2">
            <h4 className="font-medium text-sm">Payment Breakdown</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Wallet Balance:</span>
                <span className={walletCanCover ? 'text-green-600 font-medium' : ''}>
                  {numberFormat(walletBalance)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Meal Voucher Balance:</span>
                <span className={mealVoucherCanCover ? 'text-green-600 font-medium' : ''}>
                  {numberFormat(mealVoucherBalance)}
                </span>
              </div>
              <div className="flex justify-between border-t pt-1">
                <span>Total Available:</span>
                <span className={bothCanCover ? 'text-green-600 font-medium' : ''}>
                  {numberFormat(totalAvailableFunds)}
                </span>
              </div>
              {needsAdditionalPayment && (
                <div className="flex justify-between text-red-600 font-medium">
                  <span>Remaining Balance:</span>
                  <span>{numberFormat(remainingBalance)}</span>
                </div>
              )}
            </div>
          </div>

          {/* Payment Status Message */}
          <div className="text-sm">
            {walletCanCover ? (
              <p className="text-green-600 font-medium">
                ✓ Wallet balance is sufficient to cover the full amount
              </p>
            ) : mealVoucherCanCover ? (
              <p className="text-green-600 font-medium">
                ✓ Meal voucher balance is sufficient to cover the full amount
              </p>
            ) : bothCanCover ? (
              <p className="text-green-600 font-medium">
                ✓ Combined wallet and meal voucher balances can cover the full amount
              </p>
            ) : (
              <p className="text-amber-600 font-medium">
                ⚠ Additional payment of {numberFormat(remainingBalance)} is required
              </p>
            )}
          </div>

          {/* Payment Fields - Only show if additional payment is needed */}
          {needsAdditionalPayment && (
            <>
              <div className="grid gap-2">
                <Label htmlFor="paymentMode">Payment Mode</Label>
                <Select value={paymentMode} onValueChange={setPaymentMode}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment mode" />
                  </SelectTrigger>
                  <SelectContent>
                    {paymentModes.map((mode) => (
                      <SelectItem key={mode.value} value={mode.value}>
                        {mode.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="amount">Amount</Label>
                <Input
                  id="amount"
                  type="text"
                  placeholder="Enter remaining balance"
                  value={displayAmount}
                  onChange={handleAmountChange}
                />
                <p className="text-xs text-muted-foreground">
                  Required: {numberFormat(remainingBalance)}
                </p>
              </div>
            </>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isProcessing}>
            Cancel
          </Button>
          <Button onClick={handlePayment} disabled={isProcessing}>
            {isProcessing ? 'Processing...' : 'Pay'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
