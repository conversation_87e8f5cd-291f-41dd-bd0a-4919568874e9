'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { numberFormat } from '@/lib/utils';
import { toast } from 'sonner';
import { myApi } from '@/api/fetcher';

interface PaymentModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  unpaidAmount: number;
  staffId: string;
  staffName: string;
  onPaymentSuccess?: () => void;
}

export default function PaymentModal({
  open,
  setOpen,
  unpaidAmount,
  staffId,
  staffName,
  onPaymentSuccess,
}: PaymentModalProps) {
  const [paymentMode, setPaymentMode] = useState<string>('');
  const [amount, setAmount] = useState<string>('');
  const [displayAmount, setDisplayAmount] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleClose = () => {
    setOpen(false);
    setPaymentMode('');
    setAmount('');
    setDisplayAmount('');
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Remove all non-numeric characters except decimal point
    const value = e.target.value.replace(/[^\d.]/g, '');

    // Ensure only one decimal point
    const parts = value.split('.');
    if (parts.length > 2) {
      return;
    }

    // Limit decimal places to 2
    if (parts[1] && parts[1].length > 2) {
      return;
    }

    setAmount(value);

    // Format display value with commas
    if (value) {
      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        setDisplayAmount(new Intl.NumberFormat('en-US', {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        }).format(numValue));
      } else {
        setDisplayAmount(value);
      }
    } else {
      setDisplayAmount('');
    }
  };

  const handlePayment = async () => {
    if (!paymentMode) {
      toast.error('Please select a payment mode');
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (parseFloat(amount) > unpaidAmount) {
      toast.error('Amount cannot exceed unpaid credit');
      return;
    }

    try {
      setIsProcessing(true);

      const payload = {
        staffId,
        paymentMode,
        amount: parseFloat(amount),
      };

      const response = await myApi.post('/staff/pay-unpaid-credit', payload);

      if (response.status === 200) {
        toast.success('Payment processed successfully');
        handleClose();
        if (onPaymentSuccess) {
          onPaymentSuccess();
        }
      }
    } catch (error) {
      console.error('Payment failed:', error);
      toast.error('Payment failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const paymentModes = [
    { value: 'deduction', label: 'Deduction' },
    { value: 'transfer', label: 'Transfer' },
    { value: 'card', label: 'Card' },
  ];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Pay Unpaid Credit</DialogTitle>
          <DialogDescription>
            Processing payment for {staffName}
            <br />
            Outstanding amount: {numberFormat(unpaidAmount)}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="paymentMode">Payment Mode</Label>
            <Select value={paymentMode} onValueChange={setPaymentMode}>
              <SelectTrigger>
                <SelectValue placeholder="Select payment mode" />
              </SelectTrigger>
              <SelectContent>
                {paymentModes.map((mode) => (
                  <SelectItem key={mode.value} value={mode.value}>
                    {mode.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="amount">Amount</Label>
            <Input
              id="amount"
              type="text"
              placeholder="Enter amount"
              value={displayAmount}
              onChange={handleAmountChange}
            />
            <p className="text-xs text-muted-foreground">
              Maximum: {numberFormat(unpaidAmount)}
            </p>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isProcessing}>
            Cancel
          </Button>
          <Button onClick={handlePayment} disabled={isProcessing}>
            {isProcessing ? 'Processing...' : 'Pay'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
