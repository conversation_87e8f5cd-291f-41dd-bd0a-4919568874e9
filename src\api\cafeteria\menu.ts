import useSWR from 'swr';
import { useAuthSWR } from '../useAuthSWR';
import { myApi } from '../fetcher';
import { toast } from 'sonner';

export const GetAllMenu = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/menu/list?${qs.toString()}`
  );

  return {
    menu: data,
    menuLoading: isLoading,
    mutate: mutate,
  };
};

export const GetAllMenuCat = () => {
  const { data, isLoading } = useAuthSWR(`/cafeteria/menu-category/list`);

  return {
    menuCat: data,
    menuCategoryLoading: isLoading,
  };
};

//Orders

export const GetAllOrders = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/orders/list?${qs.toString()}`
  );

  return {
    orders: data,
    orderLoading: isLoading,
    mutate: mutate,
  };
};

export const GetGeneralOrders = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/orders/general/list?${qs.toString()}`
  );

  return {
    orders: data,
    orderLoading: isLoading,
    mutate: mutate,
  };
};

export const GetSpecialOrders = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/orders/list-special-order?${qs.toString()}`
  );

  return {
    specialOrders: data,
    specialLoading: isLoading,
    mutate: mutate,
  };
};

export const GetStaffSpecialOrders = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/orders/staff-special-order?${qs.toString()}`
  );

  return {
    specialOrders: data,
    specialLoading: isLoading,
    mutate: mutate,
  };
};

// Export orders to CSV
export const exportOrdersToCSV = async (
  viewType: 'my' | 'all',
  params: Record<string, any>
) => {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    // Add export parameter
    queryParams.append('export', 'csv');

    // Determine the correct API endpoint based on viewType
    const endpoint = viewType === 'all'
      ? `/cafeteria/orders/general/list?${queryParams.toString()}`
      : `/cafeteria/orders/list?${queryParams.toString()}`;

    const response = await myApi.get(endpoint, {
      responseType: 'blob', // Important for file downloads
    });

    return response.data; // Return the blob data
  } catch (error) {
    console.error('Export failed:', error);
    toast.error('Failed to export orders');
    throw error;
  }
};
