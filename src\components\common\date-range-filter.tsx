'use client';

import React, { useState, useEffect } from 'react';
import { Calendar as CalendarIcon, X } from 'lucide-react';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

interface DateRangeFilterProps {
  // Original interface
  onDateRangeChange?: (
    startDate: Date | undefined,
    endDate: Date | undefined
  ) => void;
  // New interface for URL-based filtering
  startDate?: Date | null;
  endDate?: Date | null;
  onStartDateChange?: (date: Date | null) => void;
  onEndDateChange?: (date: Date | null) => void;
  onClear?: () => void;
  // URL-based filter props
  onFilterChange?: (startDate: Date | null, endDate: Date | null) => void;
  initialStartDate?: Date | null;
  initialEndDate?: Date | null;
  className?: string;
}

const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  onDateRangeChange,
  startDate: externalStartDate,
  endDate: externalEndDate,
  onStartDateChange,
  onEndDateChange,
  onClear: externalClear,
  onFilterChange,
  initialStartDate,
  initialEndDate,
  className,
}) => {
  // Use internal state if external state is not provided
  const [internalStartDate, setInternalStartDate] = useState<Date | undefined>(
    initialStartDate || undefined
  );
  const [internalEndDate, setInternalEndDate] = useState<Date | undefined>(
    initialEndDate || undefined
  );
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  // Determine if we're using internal, external, or URL-based state management
  const isExternalState =
    onStartDateChange !== undefined && onEndDateChange !== undefined;
  const isUrlBasedState = onFilterChange !== undefined;

  // Get the current start and end dates based on state management approach
  const startDate = isExternalState
    ? (externalStartDate as Date | undefined)
    : isUrlBasedState
      ? (initialStartDate as Date | undefined)
      : internalStartDate;
  const endDate = isExternalState
    ? (externalEndDate as Date | undefined)
    : isUrlBasedState
      ? (initialEndDate as Date | undefined)
      : internalEndDate;

  // Set start date function that works with all interfaces
  const setStartDate = (date: Date | undefined) => {
    if (isExternalState) {
      onStartDateChange?.(date as Date | null);
    } else if (isUrlBasedState) {
      // For URL-based filtering, we don't update local state directly
      // The parent component will handle URL updates
    } else {
      setInternalStartDate(date);
    }
  };

  // Set end date function that works with all interfaces
  const setEndDate = (date: Date | undefined) => {
    if (isExternalState) {
      onEndDateChange?.(date as Date | null);
    } else if (isUrlBasedState) {
      // For URL-based filtering, we don't update local state directly
      // The parent component will handle URL updates
    } else {
      setInternalEndDate(date);
    }
  };

  // Update parent component when date range changes (for original interface)
  useEffect(() => {
    if (onDateRangeChange) {
      onDateRangeChange(startDate, endDate);
    }
  }, [startDate, endDate, onDateRangeChange]);

  // Handle date selection
  const handleSelect = (date: Date | undefined) => {
    if (!startDate || (startDate && endDate)) {
      // If no start date is selected or both dates are already selected, set start date
      if (isUrlBasedState) {
        onFilterChange?.(date || null, null);
      } else {
        setStartDate(date);
        setEndDate(undefined);
      }
    } else {
      // If only start date is selected and the new date is after start date, set end date
      if (date && date >= startDate) {
        if (isUrlBasedState) {
          onFilterChange?.(startDate, date);
        } else {
          setEndDate(date);
        }
        setIsCalendarOpen(false); // Close calendar after selecting end date
      } else {
        // If new date is before start date, swap them
        if (isUrlBasedState) {
          onFilterChange?.(date || null, startDate);
        } else {
          setEndDate(startDate);
          setStartDate(date);
        }
        setIsCalendarOpen(false); // Close calendar after selecting end date
      }
    }
  };

  // Clear date range
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (isExternalState) {
      externalClear?.();
    } else if (isUrlBasedState) {
      onFilterChange?.(null, null);
    } else {
      setInternalStartDate(undefined);
      setInternalEndDate(undefined);
      onDateRangeChange?.(undefined, undefined);
    }
  };

  // Format date range for display
  const formatDateRange = () => {
    if (startDate && endDate) {
      return `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`;
    }
    if (startDate) {
      return `${format(startDate, 'MMM d, yyyy')} - Select end date`;
    }
    return 'Filter by date';
  };

  return (
    <div className={cn('relative', className)}>
      <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'w-full justify-between text-left font-normal',
              !startDate && 'text-muted-foreground'
            )}
          >
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-4 w-4" />
              <span>{formatDateRange()}</span>
            </div>
            {(startDate || endDate) && (
              <X
                className="h-4 w-4 opacity-70 hover:opacity-100"
                onClick={handleClear}
              />
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={endDate || startDate}
            onSelect={handleSelect}
            initialFocus
            defaultMonth={startDate}
            numberOfMonths={1}
            disabled={(date) => {
              // Disable dates more than 1 year in the past or future
              const oneYearAgo = new Date();
              oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
              const oneYearFromNow = new Date();
              oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
              return date < oneYearAgo || date > oneYearFromNow;
            }}
          />
          <div className="p-3 border-t border-border">
            <div className="flex justify-between items-center">
              <div className="text-sm">
                {startDate && (
                  <span className="font-medium">
                    {format(startDate, 'MMM d, yyyy')}
                  </span>
                )}
                {startDate && endDate && <span> - </span>}
                {endDate && (
                  <span className="font-medium">
                    {format(endDate, 'MMM d, yyyy')}
                  </span>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClear}
                className="h-7 px-3"
              >
                Clear
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default DateRangeFilter;
